#include <linux/bpf.h>
#include <linux/if_ether.h>
#include <linux/ip.h>
#include <linux/in.h>
#include <linux/udp.h>
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_endian.h>

// Default values - now configurable via BPF maps
#define DEFAULT_FIVEM_SERVER_PORT   30120           // Primary FiveM server port
#define DEFAULT_FIVEM_GAME_PORT1    6672            // Internal game communication port
#define DEFAULT_FIVEM_GAME_PORT2    6673            // Alternative game communication port
#define DEFAULT_RATE_LIMIT          1000            // Default packets per second per IP
#define DEFAULT_GLOBAL_RATE_LIMIT   50000           // Default global packets per second limit
#define DEFAULT_SUBNET_RATE_LIMIT   5000            // Default subnet (/24) packets per second limit

// FiveM Protocol Constants
#define OOB_PACKET_MARKER   0xFFFFFFFF      // Out-of-band packet identifier
#define ENET_MAX_PEER_ID    0x0FFF          // Maximum ENet peer ID (4095)
#define MIN_PACKET_SIZE     4               // Minimum valid packet size
#define MAX_PACKET_SIZE     2400            // Maximum sync packet size
#define MAX_VOICE_SIZE      8192            // Maximum voice packet size
#define ENET_HEADER_SIZE    4               // Minimum ENet header size
#define MAX_TOKEN_AGE       7200000000000ULL // 2 hours in nanoseconds
#define MAX_SEQUENCE_WINDOW 100             // Acceptable out-of-order packet window

// Attack Classification Types
enum attack_type {
    ATTACK_NONE = 0,
    ATTACK_RATE_LIMIT = 1,
    ATTACK_INVALID_PROTOCOL = 2,
    ATTACK_REPLAY = 3,
    ATTACK_STATE_VIOLATION = 4,
    ATTACK_CHECKSUM_FAIL = 5,
    ATTACK_SIZE_VIOLATION = 6,
    ATTACK_SEQUENCE_ANOMALY = 7,
    ATTACK_TOKEN_REUSE = 8
};

// Connection State Machine
enum connection_state {
    STATE_INITIAL = 0,
    STATE_OOB_SENT = 1,
    STATE_CONNECTING = 2,
    STATE_CONNECTED = 3,
    STATE_SUSPICIOUS = 4
};

// Complete FiveM message type hashes (critical for DPI)
// Based on code/shared/net/PacketNames.h from FiveM codebase
#define MSG_ARRAY_UPDATE_HASH       0x0976e783      // msgArrayUpdate
#define MSG_CONVARS_HASH            0x6acbd583      // msgConVars
#define MSG_CONFIRM_HASH            0xba96192a      // msgConfirm
#define MSG_END_HASH                0xca569e63      // msgEnd
#define MSG_ENTITY_CREATE_HASH      0x0f216a2a      // msgEntityCreate
#define MSG_FRAME_HASH              0x53fffa3f      // msgFrame
#define MSG_HE_HOST_HASH            0x86e9f87b      // msgHeHost
#define MSG_I_HOST_HASH             0xb3ea30de      // msgIHost
#define MSG_I_QUIT_HASH             0x522cadd1      // msgIQuit
#define MSG_NET_EVENT_HASH          0x7337fd7a      // msgNetEvent
#define MSG_NET_GAME_EVENT_HASH     0x100d66a8      // msgNetGameEvent
#define MSG_OBJECT_IDS_HASH         0x48e39581      // msgObjectIds
#define MSG_PACKED_ACKS_HASH        0x258dfdb4      // msgPackedAcks
#define MSG_PACKED_CLONES_HASH      0x81e1c835      // msgPackedClones
#define MSG_PAYMENT_REQUEST_HASH    0x073b065b      // msgPaymentRequest
#define MSG_REQUEST_OBJECT_IDS_HASH 0xb8e611cf      // msgRequestObjectIds
#define MSG_RES_START_HASH          0xafe4cd4a      // msgResStart
#define MSG_RES_STOP_HASH           0x45e855d7      // msgResStop
#define MSG_ROUTE_HASH              0xe938445b      // msgRoute
#define MSG_RPC_NATIVE_HASH         0x211cab17      // msgRpcNative
#define MSG_SERVER_COMMAND_HASH     0xb18d4fc4      // msgServerCommand
#define MSG_SERVER_EVENT_HASH       0xfa776e18      // msgServerEvent
#define MSG_STATE_BAG_HASH          0xde3d1a59      // msgStateBag
#define MSG_TIME_SYNC_HASH          0xe56e37ed      // msgTimeSync
#define MSG_TIME_SYNC_REQ_HASH      0x1c1303f8      // msgTimeSyncReq
#define MSG_WORLD_GRID3_HASH        0x852c1561      // msgWorldGrid3
#define MSG_GAME_STATE_ACK_HASH     0xa5d4e2bc      // gameStateAck
#define MSG_GAME_STATE_NACK_HASH    0xd2f86a6e      // gameStateNAck

// CRITICAL FIX 1: Configurable server configuration map
struct server_config {
    __u32 server_ip;                    // Target server IP (configurable)
    __u16 server_port;                  // Primary FiveM server port
    __u16 game_port1;                   // Internal game communication port
    __u16 game_port2;                   // Alternative game communication port
    __u32 rate_limit;                   // Packets per second per IP
    __u32 global_rate_limit;            // Global packets per second limit
    __u32 subnet_rate_limit;            // Subnet (/24) packets per second limit
    __u8 enable_checksum_validation;    // Enable/disable CRC32 validation
    __u8 strict_enet_validation;        // Enable strict ENet header validation
    __u8 reserved[3];                   // Padding for future use
};

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct server_config);
} server_config_map SEC(".maps");

// Per-IP rate limiting map (hash table for better scalability)
struct {
    __uint(type, BPF_MAP_TYPE_LRU_HASH);
    __uint(max_entries, 10000);
    __type(key, __u32);     // Source IP address
    __type(value, __u64);   // Last packet timestamp
} rate_limit_map SEC(".maps");

// Packet statistics map
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 4); // 0: dropped, 1: passed, 2: invalid_protocol, 3: rate_limited
    __type(key, __u32);
    __type(value, __u64);
} packet_count_map SEC(".maps");

// Enhanced connection token tracking with replay protection
struct connection_token_state {
    __u32 source_ip;
    __u64 first_seen;
    __u32 usage_count;
    __u16 sequence_number;
};

struct {
    __uint(type, BPF_MAP_TYPE_LRU_HASH);
    __uint(max_entries, 5000);
    __type(key, __u32);     // Connection token hash
    __type(value, struct connection_token_state);
} enhanced_token_map SEC(".maps");

// Peer sequence number validation
struct peer_state {
    __u16 last_sequence;
    __u64 last_update;
    __u32 out_of_order_count;
};

struct {
    __uint(type, BPF_MAP_TYPE_LRU_HASH);
    __uint(max_entries, 4096);
    __type(key, __u64);     // (src_ip << 32) | peer_id
    __type(value, struct peer_state);
} peer_sequence_map SEC(".maps");

// Connection state machine tracking
struct connection_context {
    enum connection_state state;
    __u64 state_timestamp;
    __u32 packet_count;
    __u8 violations;
};

struct {
    __uint(type, BPF_MAP_TYPE_LRU_HASH);
    __uint(max_entries, 2048);
    __type(key, __u32);     // Source IP
    __type(value, struct connection_context);
} connection_state_map SEC(".maps");

// Attack logging and classification
struct attack_stats {
    __u64 count;
    __u64 last_seen;
    __u32 source_ip;
    __u16 attack_type;
};

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1000);
    __type(key, __u32);     // Attack ID (incremental)
    __type(value, struct attack_stats);
} attack_log_map SEC(".maps");

// Performance metrics tracking
struct perf_metrics {
    __u64 total_packets;
    __u64 processing_time_ns;
    __u64 map_lookup_time_ns;
    __u32 max_processing_time_ns;
    __u32 avg_packet_size;
};

struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct perf_metrics);
} perf_metrics_map SEC(".maps");

// Hierarchical rate limiting - Global level
struct global_rate_state {
    __u64 packet_count;
    __u64 window_start;
    __u32 current_limit;
};

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct global_rate_state);
} global_rate_map SEC(".maps");

// Hierarchical rate limiting - Subnet level
struct subnet_rate_state {
    __u64 packet_count;
    __u64 window_start;
    __u32 active_ips;
};

struct {
    __uint(type, BPF_MAP_TYPE_LRU_HASH);
    __uint(max_entries, 1024);
    __type(key, __u32);     // Subnet (/24)
    __type(value, struct subnet_rate_state);
} subnet_rate_map SEC(".maps");

// Enhanced statistics with attack classification
struct enhanced_stats {
    __u64 dropped;
    __u64 passed;
    __u64 invalid_protocol;
    __u64 rate_limited;
    __u64 token_violations;
    __u64 sequence_violations;
    __u64 state_violations;
    __u64 checksum_failures;
};

struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct enhanced_stats);
} enhanced_stats_map SEC(".maps");

// Optimized FiveM message hash validation using grouped lookup
// Validates against all 28 known FiveM message types from PacketNames.h
static __always_inline int is_valid_fivem_message_hash(__u32 hash) {
    // Group hashes by first byte for faster lookup (performance optimization)
    __u8 first_byte = hash & 0xFF;

    switch (first_byte) {
        case 0x83: // MSG_ARRAY_UPDATE_HASH, MSG_CONVARS_HASH
            return (hash == 0x0976e783 || hash == 0x6acbd583);
        case 0x2a: // MSG_CONFIRM_HASH, MSG_ENTITY_CREATE_HASH
            return (hash == 0xba96192a || hash == 0x0f216a2a);
        case 0x63: // MSG_END_HASH
            return (hash == 0xca569e63);
        case 0x3f: // MSG_FRAME_HASH
            return (hash == 0x53fffa3f);
        case 0x7b: // MSG_HE_HOST_HASH
            return (hash == 0x86e9f87b);
        case 0xde: // MSG_I_HOST_HASH
            return (hash == 0xb3ea30de);
        case 0xd1: // MSG_I_QUIT_HASH
            return (hash == 0x522cadd1);
        case 0x7a: // MSG_NET_EVENT_HASH
            return (hash == 0x7337fd7a);
        case 0xa8: // MSG_NET_GAME_EVENT_HASH
            return (hash == 0x100d66a8);
        case 0x81: // MSG_OBJECT_IDS_HASH
            return (hash == 0x48e39581);
        case 0xb4: // MSG_PACKED_ACKS_HASH
            return (hash == 0x258dfdb4);
        case 0x35: // MSG_PACKED_CLONES_HASH
            return (hash == 0x81e1c835);
        case 0x5b: // MSG_PAYMENT_REQUEST_HASH
            return (hash == 0x073b065b);
        case 0xcf: // MSG_REQUEST_OBJECT_IDS_HASH
            return (hash == 0xb8e611cf);
        case 0x4a: // MSG_RES_START_HASH
            return (hash == 0xafe4cd4a);
        case 0xd7: // MSG_RES_STOP_HASH
            return (hash == 0x45e855d7);
        case 0x5b: // MSG_ROUTE_HASH (collision with PAYMENT_REQUEST, check both)
            return (hash == 0xe938445b || hash == 0x073b065b);
        case 0x17: // MSG_RPC_NATIVE_HASH
            return (hash == 0x211cab17);
        case 0xc4: // MSG_SERVER_COMMAND_HASH
            return (hash == 0xb18d4fc4);
        case 0x18: // MSG_SERVER_EVENT_HASH
            return (hash == 0xfa776e18);
        case 0x59: // MSG_STATE_BAG_HASH
            return (hash == 0xde3d1a59);
        case 0xed: // MSG_TIME_SYNC_HASH
            return (hash == 0xe56e37ed);
        case 0xf8: // MSG_TIME_SYNC_REQ_HASH
            return (hash == 0x1c1303f8);
        case 0x61: // MSG_WORLD_GRID3_HASH
            return (hash == 0x852c1561);
        case 0xbc: // MSG_GAME_STATE_ACK_HASH
            return (hash == 0xa5d4e2bc);
        case 0x6e: // MSG_GAME_STATE_NACK_HASH
            return (hash == 0xd2f86a6e);
        default:
            return 0;
    }
}

// CRITICAL FIX 4: Helper function to get server configuration with defaults
static __always_inline struct server_config* get_server_config() {
    __u32 key = 0;
    struct server_config *config = bpf_map_lookup_elem(&server_config_map, &key);
    return config; // Returns NULL if not configured, caller should handle defaults
}

// CRITICAL FIX 4: Helper function to apply configurable rate limiting per source IP
static __always_inline int apply_rate_limit(__u32 src_ip, __u32 rate_limit) {
    __u64 now = bpf_ktime_get_ns();
    __u64 *last_time = bpf_map_lookup_elem(&rate_limit_map, &src_ip);

    if (last_time) {
        // Check if enough time has passed (1 second / rate_limit)
        if (now - *last_time < (1000000000ULL / rate_limit)) {
            return 0; // Rate limit exceeded
        }
    }

    // Update timestamp
    bpf_map_update_elem(&rate_limit_map, &src_ip, &now, BPF_ANY);
    return 1; // Allow packet
}

// Enhanced statistics update with attack classification
static __always_inline void update_enhanced_stats(__u32 stat_type) {
    __u32 key = 0;
    struct enhanced_stats *stats = bpf_map_lookup_elem(&enhanced_stats_map, &key);
    if (!stats) return;

    switch (stat_type) {
        case 0: __sync_fetch_and_add(&stats->dropped, 1); break;
        case 1: __sync_fetch_and_add(&stats->passed, 1); break;
        case 2: __sync_fetch_and_add(&stats->invalid_protocol, 1); break;
        case 3: __sync_fetch_and_add(&stats->rate_limited, 1); break;
        case 4: __sync_fetch_and_add(&stats->token_violations, 1); break;
        case 5: __sync_fetch_and_add(&stats->sequence_violations, 1); break;
        case 6: __sync_fetch_and_add(&stats->state_violations, 1); break;
        case 7: __sync_fetch_and_add(&stats->checksum_failures, 1); break;
    }
}

// Legacy stats update for backward compatibility
static __always_inline void update_stats(__u32 stat_type) {
    __u64 *count = bpf_map_lookup_elem(&packet_count_map, &stat_type);
    if (count) {
        __sync_fetch_and_add(count, 1);
    }
    update_enhanced_stats(stat_type);
}

// Attack logging function
static __always_inline void log_attack(__u32 src_ip, enum attack_type type) {
    static __u32 attack_id = 0;

    struct attack_stats stats = {
        .count = 1,
        .last_seen = bpf_ktime_get_ns(),
        .source_ip = src_ip,
        .attack_type = type
    };

    __u32 id = __sync_fetch_and_add(&attack_id, 1);
    if (id < 1000) { // Prevent overflow
        bpf_map_update_elem(&attack_log_map, &id, &stats, BPF_ANY);
    }
}

// Performance metrics tracking
static __always_inline void update_perf_metrics(__u64 start_time, __u32 packet_size) {
    __u64 end_time = bpf_ktime_get_ns();
    __u64 processing_time = end_time - start_time;

    __u32 key = 0;
    struct perf_metrics *metrics = bpf_map_lookup_elem(&perf_metrics_map, &key);
    if (!metrics) return;

    __sync_fetch_and_add(&metrics->total_packets, 1);
    __sync_fetch_and_add(&metrics->processing_time_ns, processing_time);

    if (processing_time > metrics->max_processing_time_ns) {
        metrics->max_processing_time_ns = processing_time;
    }

    // Update average packet size (exponential moving average)
    metrics->avg_packet_size = (metrics->avg_packet_size * 7 + packet_size) / 8;
}

// Enhanced connection token validation with replay protection
static __always_inline int validate_connection_token(__u32 token_hash, __u32 src_ip) {
    struct connection_token_state *state = bpf_map_lookup_elem(&enhanced_token_map, &token_hash);
    __u64 now = bpf_ktime_get_ns();

    if (!state) {
        // New token - create entry
        struct connection_token_state new_state = {
            .source_ip = src_ip,
            .first_seen = now,
            .usage_count = 1,
            .sequence_number = 0
        };
        bpf_map_update_elem(&enhanced_token_map, &token_hash, &new_state, BPF_ANY);
        return 1;
    }

    // Validate IP consistency (anti-spoofing)
    if (state->source_ip != src_ip) {
        log_attack(src_ip, ATTACK_TOKEN_REUSE);
        return 0;
    }

    // Validate usage count (max 3 retries as per FiveM)
    if (state->usage_count > 3) {
        log_attack(src_ip, ATTACK_TOKEN_REUSE);
        return 0;
    }

    // Validate token age (expire after 2 hours)
    if (now - state->first_seen > MAX_TOKEN_AGE) {
        log_attack(src_ip, ATTACK_REPLAY);
        return 0;
    }

    state->usage_count++;
    return 1;
}

// Sequence number validation to prevent replay attacks
static __always_inline int validate_sequence_number(__u32 src_ip, __u16 peer_id, __u16 sequence) {
    __u64 key = ((__u64)src_ip << 32) | peer_id;
    struct peer_state *state = bpf_map_lookup_elem(&peer_sequence_map, &key);
    __u64 now = bpf_ktime_get_ns();

    if (!state) {
        struct peer_state new_state = {
            .last_sequence = sequence,
            .last_update = now,
            .out_of_order_count = 0
        };
        bpf_map_update_elem(&peer_sequence_map, &key, &new_state, BPF_ANY);
        return 1;
    }

    // Allow reasonable out-of-order delivery (window of 100)
    __s16 seq_diff = sequence - state->last_sequence;
    if (seq_diff > 0 && seq_diff < MAX_SEQUENCE_WINDOW) {
        state->last_sequence = sequence;
        state->last_update = now;
        return 1;
    }

    // Track excessive out-of-order packets (potential attack)
    if (seq_diff < -MAX_SEQUENCE_WINDOW || seq_diff > 1000) {
        state->out_of_order_count++;
        if (state->out_of_order_count > 10) {
            log_attack(src_ip, ATTACK_SEQUENCE_ANOMALY);
            return 0; // Block suspicious peer
        }
    }

    return 1;
}

// Protocol state machine validation
static __always_inline int validate_protocol_state(__u32 src_ip, __u32 first_word, __u32 msg_hash) {
    struct connection_context *ctx = bpf_map_lookup_elem(&connection_state_map, &src_ip);
    __u64 now = bpf_ktime_get_ns();

    if (!ctx) {
        // New connection must start with OOB
        if (first_word != OOB_PACKET_MARKER) {
            log_attack(src_ip, ATTACK_STATE_VIOLATION);
            return 0;
        }

        struct connection_context new_ctx = {
            .state = STATE_OOB_SENT,
            .state_timestamp = now,
            .packet_count = 1,
            .violations = 0
        };
        bpf_map_update_elem(&connection_state_map, &src_ip, &new_ctx, BPF_ANY);
        return 1;
    }

    // State transition validation
    switch (ctx->state) {
        case STATE_OOB_SENT:
            if (msg_hash == MSG_CONFIRM_HASH) {
                ctx->state = STATE_CONNECTING;
                ctx->state_timestamp = now;
                return 1;
            }
            break;
        case STATE_CONNECTING:
            if (msg_hash == MSG_I_HOST_HASH || msg_hash == MSG_HE_HOST_HASH) {
                ctx->state = STATE_CONNECTED;
                ctx->state_timestamp = now;
                return 1;
            }
            break;
        case STATE_CONNECTED:
            // Allow normal game traffic
            return 1;
        case STATE_SUSPICIOUS:
            // Block all traffic from suspicious IPs
            log_attack(src_ip, ATTACK_STATE_VIOLATION);
            return 0;
    }

    // Invalid state transition
    ctx->violations++;
    if (ctx->violations > 3) {
        ctx->state = STATE_SUSPICIOUS;
        log_attack(src_ip, ATTACK_STATE_VIOLATION);
        return 0;
    }

    return 1;
}

// CRITICAL FIX 4: Hierarchical rate limiting with configurable limits
static __always_inline int hierarchical_rate_limit(__u32 src_ip, struct server_config *config) {
    __u64 now = bpf_ktime_get_ns();
    __u64 window_size = 1000000000ULL; // 1 second

    // Use configured limits or defaults
    __u32 global_limit = config ? config->global_rate_limit : DEFAULT_GLOBAL_RATE_LIMIT;
    __u32 subnet_limit = config ? config->subnet_rate_limit : DEFAULT_SUBNET_RATE_LIMIT;
    __u32 ip_limit = config ? config->rate_limit : DEFAULT_RATE_LIMIT;

    // Global rate limiting (prevents server overload)
    __u32 global_key = 0;
    struct global_rate_state *global = bpf_map_lookup_elem(&global_rate_map, &global_key);
    if (global) {
        if (now - global->window_start > window_size) {
            global->packet_count = 1;
            global->window_start = now;
        } else {
            global->packet_count++;
            if (global->packet_count > global_limit) {
                log_attack(src_ip, ATTACK_RATE_LIMIT);
                return 0;
            }
        }
    }

    // Subnet rate limiting (prevents subnet-based attacks)
    __u32 subnet = src_ip & 0xFFFFFF00; // /24 subnet
    struct subnet_rate_state *subnet_state = bpf_map_lookup_elem(&subnet_rate_map, &subnet);
    if (subnet_state) {
        if (now - subnet_state->window_start > window_size) {
            subnet_state->packet_count = 1;
            subnet_state->window_start = now;
        } else {
            subnet_state->packet_count++;
            if (subnet_state->packet_count > subnet_limit) {
                log_attack(src_ip, ATTACK_RATE_LIMIT);
                return 0;
            }
        }
    } else {
        struct subnet_rate_state new_subnet = {
            .packet_count = 1,
            .window_start = now,
            .active_ips = 1
        };
        bpf_map_update_elem(&subnet_rate_map, &subnet, &new_subnet, BPF_ANY);
    }

    // Per-IP rate limiting with configurable limit
    return apply_rate_limit(src_ip, ip_limit);
}

// CRITICAL FIX 3: Optimized and optional checksum validation
// Simple hash validation instead of full CRC32 for performance
static __always_inline __u32 calculate_simple_hash(__u8 *data, __u32 len) {
    __u32 hash = 0x811c9dc5; // FNV-1a initial value
    __u32 max_len = len < 32 ? len : 32; // Limit processing to first 32 bytes for performance

    for (__u32 i = 0; i < max_len; i++) {
        hash ^= data[i];
        hash *= 0x01000193; // FNV-1a prime
    }
    return hash;
}

// CRITICAL FIX 3: Optional ENet checksum validation with performance optimization
static __always_inline int validate_enet_checksum(void *payload, __u32 len, void *data_end, __u8 enable_validation) {
    // Skip validation if disabled for performance
    if (!enable_validation) {
        return 1;
    }

    if (len < 8 || (void *)payload + len > data_end) return 1; // Skip if too small

    // Use simple hash validation instead of full CRC32 for better performance
    // This provides reasonable protection against corrupted packets while being much faster
    __u32 *checksum_ptr = (__u32*)((char*)payload + len - 4);
    if ((void*)(checksum_ptr + 1) > data_end) return 1; // Skip if can't read checksum

    __u32 provided_checksum = *checksum_ptr;
    __u32 calculated_hash = calculate_simple_hash((__u8*)payload, len - 4);

    // Use a simple comparison that's good enough for attack detection
    // Full CRC32 validation can be done at application level if needed
    if ((provided_checksum ^ calculated_hash) & 0xFFFF0000) {
        return 0; // Likely corrupted or malicious packet
    }

    return 1; // Packet appears valid
}

SEC("xdp_fivem_advanced")
int fivem_xdp_advanced(struct xdp_md *ctx) {
    void *data_end = (void *)(long)ctx->data_end;
    void *data = (void *)(long)ctx->data;

    // Performance tracking - start timer
    __u64 start_time = bpf_ktime_get_ns();

    // CRITICAL FIX 1: Get server configuration (with fallback to defaults)
    struct server_config *config = get_server_config();

    // Use configured values or defaults
    __u32 target_server_ip = config ? config->server_ip : 0; // 0 means accept any IP if not configured
    __u16 server_port = config ? config->server_port : DEFAULT_FIVEM_SERVER_PORT;
    __u16 game_port1 = config ? config->game_port1 : DEFAULT_FIVEM_GAME_PORT1;
    __u16 game_port2 = config ? config->game_port2 : DEFAULT_FIVEM_GAME_PORT2;

    // Early packet size check (performance optimization)
    __u32 packet_size = data_end - data;
    if (packet_size < 42) return XDP_ABORTED; // Eth+IP+UDP minimum

    // Parse headers in single pass (performance optimization)
    struct ethhdr *eth = data;
    struct iphdr *ip = (void*)eth + sizeof(*eth);
    struct udphdr *udp = (void*)ip + (ip->ihl * 4);

    // Bounds checking for all headers at once
    if ((void *)(eth + 1) > data_end ||
        (void *)(ip + 1) > data_end ||
        (void *)(udp + 1) > data_end ||
        ip->ihl < 5) {
        return XDP_ABORTED;
    }

    // CRITICAL FIX 1: Configurable server IP validation
    // If target_server_ip is 0, accept packets to any IP (useful for multi-server setups)
    // Otherwise, only accept packets destined for the configured server IP
    if (eth->h_proto != bpf_htons(ETH_P_IP) ||
        ip->protocol != IPPROTO_UDP ||
        (target_server_ip != 0 && ip->daddr != bpf_htonl(target_server_ip))) {
        return XDP_PASS;
    }

    // CRITICAL FIX 4: Configurable port validation
    __u16 dest_port = bpf_ntohs(udp->dest);

    // Check if it's one of our target ports using configured values
    if (dest_port != server_port &&
        dest_port != game_port1 &&
        dest_port != game_port2) {
        return XDP_PASS;
    }

    // Get payload information
    void *payload = (void *)udp + sizeof(struct udphdr);
    __u32 payload_len = bpf_ntohs(udp->len) - sizeof(struct udphdr);
    __u32 src_ip = ip->saddr;

    // CRITICAL FIX 4: Hierarchical rate limiting with configurable limits
    if (!hierarchical_rate_limit(src_ip, config)) {
        update_stats(3); // rate_limited
        update_perf_metrics(start_time, packet_size);
        return XDP_DROP;
    }

    // Validate packet size constraints
    if (payload_len < MIN_PACKET_SIZE) {
        update_stats(2); // invalid_protocol
        log_attack(src_ip, ATTACK_SIZE_VIOLATION);
        update_perf_metrics(start_time, packet_size);
        return XDP_DROP;
    }

    // Different size limits for different ports (using configured port values)
    __u32 max_size = (dest_port == server_port) ? MAX_PACKET_SIZE : MAX_VOICE_SIZE;
    if (payload_len > max_size) {
        update_stats(2); // invalid_protocol
        log_attack(src_ip, ATTACK_SIZE_VIOLATION);
        update_perf_metrics(start_time, packet_size);
        return XDP_DROP;
    }

    // Ensure we can read the first 4 bytes for protocol validation
    if ((void *)payload + 4 > data_end) {
        update_perf_metrics(start_time, packet_size);
        return XDP_ABORTED;
    }

    __u32 first_word = *(__u32*)payload;

    // Handle Out-of-Band (OOB) packets - these start with 0xFFFFFFFF
    if (first_word == OOB_PACKET_MARKER) {
        // OOB packets need at least 8 bytes (marker + 4 bytes data)
        if (payload_len < 8) {
            update_stats(2); // invalid_protocol
            log_attack(src_ip, ATTACK_INVALID_PROTOCOL);
            update_perf_metrics(start_time, packet_size);
            return XDP_DROP;
        }

        // Extract connection token for validation (if present)
        if (payload_len >= 12 && (void *)payload + 12 <= data_end) {
            __u32 token_hash = *(__u32*)((char*)payload + 8);
            if (!validate_connection_token(token_hash, src_ip)) {
                update_enhanced_stats(4); // token_violations
                update_perf_metrics(start_time, packet_size);
                return XDP_DROP;
            }
        }

        // Protocol state validation
        if (!validate_protocol_state(src_ip, first_word, 0)) {
            update_enhanced_stats(6); // state_violations
            update_perf_metrics(start_time, packet_size);
            return XDP_DROP;
        }

        update_stats(1); // passed
        update_perf_metrics(start_time, packet_size);
        return XDP_PASS;
    }

    // CRITICAL FIX 2: Corrected ENet packet parsing based on actual ENet protocol
    // ENet packet structure (from ENet documentation and FiveM implementation):
    // Bytes 0-1: Peer ID (12 bits) + Flags (4 bits)
    // Bytes 2-3: Sequence number (for reliable packets)
    // Bytes 4+: Packet data

    __u16 enet_header = *(__u16*)payload;
    __u16 peer_id = enet_header & ENET_MAX_PEER_ID;  // Extract peer ID (lower 12 bits)
    __u16 flags = (enet_header >> 12) & 0xF;         // Extract flags (upper 4 bits)
    __u16 sequence = 0;

    // Validate peer ID range (0-4095 as per ENet specification)
    if (peer_id > ENET_MAX_PEER_ID) {
        update_stats(2); // invalid_protocol
        log_attack(src_ip, ATTACK_INVALID_PROTOCOL);
        update_perf_metrics(start_time, packet_size);
        return XDP_DROP;
    }

    // Extract sequence number for reliable packets (if available and packet is large enough)
    if (payload_len >= 4 && (void *)payload + 4 <= data_end) {
        sequence = *(__u16*)((char*)payload + 2);

        // Only validate sequence for reliable packets (flag check)
        // ENet reliable packets have specific flag patterns
        if (flags & 0x1) { // Reliable packet flag
            if (!validate_sequence_number(src_ip, peer_id, sequence)) {
                update_enhanced_stats(5); // sequence_violations
                update_perf_metrics(start_time, packet_size);
                return XDP_DROP;
            }
        }
    }

    // CRITICAL FIX 3: Optional ENet checksum validation with configurable enable/disable
    __u8 enable_checksum = config ? config->enable_checksum_validation : 1; // Default enabled
    if (payload_len >= 12 && !validate_enet_checksum(payload, payload_len, data_end, enable_checksum)) {
        update_enhanced_stats(7); // checksum_failures
        log_attack(src_ip, ATTACK_CHECKSUM_FAIL);
        update_perf_metrics(start_time, packet_size);
        return XDP_DROP;
    }

    // For packets with enough data, validate message type hash
    __u32 msg_hash = 0;
    if (payload_len >= 8) {
        // Message hash is typically at offset 4 after the ENet header
        if ((void *)payload + 8 > data_end) {
            update_perf_metrics(start_time, packet_size);
            return XDP_ABORTED;
        }

        msg_hash = *(__u32*)((char*)payload + 4);

        // CRITICAL FIX 4: Only validate hash for main server port using configured port
        if (dest_port == server_port && !is_valid_fivem_message_hash(msg_hash)) {
            update_stats(2); // invalid_protocol
            log_attack(src_ip, ATTACK_INVALID_PROTOCOL);
            update_perf_metrics(start_time, packet_size);
            return XDP_DROP;
        }

        // Protocol state validation with message hash
        if (!validate_protocol_state(src_ip, first_word, msg_hash)) {
            update_enhanced_stats(6); // state_violations
            update_perf_metrics(start_time, packet_size);
            return XDP_DROP;
        }
    }

    // Packet passed all validation checks
    update_stats(1); // passed
    update_perf_metrics(start_time, packet_size);
    return XDP_PASS;
}

char _license[] SEC("license") = "MIT";